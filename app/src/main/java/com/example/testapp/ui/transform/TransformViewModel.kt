package com.example.testapp.ui.transform

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel

class TransformViewModel : ViewModel() {

    private val _texts = MutableLiveData<List<String>>().apply {
        value = listOf(
            "Classroom",
            "Math",
            "Science",
            "History",
            "English",
            "Documents",
            "Photos",
            "Downloads",
            "Projects",
            "Presentations",
            "Spreadsheets",
            "Videos"
        )
    }

    val texts: LiveData<List<String>> = _texts
}