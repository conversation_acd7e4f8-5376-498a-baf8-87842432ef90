package com.example.testapp

import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import android.view.View
import com.google.android.material.snackbar.Snackbar
import com.google.android.material.tabs.TabLayout
import com.google.android.material.navigation.NavigationView
import com.google.android.material.floatingactionbutton.FloatingActionButton
import androidx.navigation.findNavController
import androidx.navigation.fragment.NavHostFragment
import androidx.navigation.ui.AppBarConfiguration
import androidx.navigation.ui.navigateUp
import androidx.appcompat.app.AppCompatActivity
import com.example.testapp.databinding.ActivityMainBinding

class MainActivity : AppCompatActivity() {

    private lateinit var appBarConfiguration: AppBarConfiguration
    private lateinit var binding: ActivityMainBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // Setup navigation
        val navHostFragment =
            (supportFragmentManager.findFragmentById(R.id.nav_host_fragment_content_main) as NavHostFragment?)!!
        val navController = navHostFragment.navController

        // Setup tabs
        setupTabs()

        // Setup floating action buttons
        setupFloatingActionButtons()

        // Simple app bar configuration for modern design
        appBarConfiguration = AppBarConfiguration(
            setOf(R.id.nav_transform, R.id.nav_reflow, R.id.nav_slideshow)
        )
    }

    private fun setupTabs() {
        val tabLayout: TabLayout? = findViewById(R.id.tab_layout)
        tabLayout?.let { tabs ->
            tabs.addTab(tabs.newTab().setText("My Drive"))
            tabs.addTab(tabs.newTab().setText("Local Files"))

            // Set the first tab as selected
            tabs.getTabAt(0)?.select()

            tabs.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
                override fun onTabSelected(tab: TabLayout.Tab?) {
                    // Handle tab selection
                    when (tab?.position) {
                        0 -> {
                            // My Drive selected
                            val navController = findNavController(R.id.nav_host_fragment_content_main)
                            navController.navigate(R.id.nav_transform)
                        }
                        1 -> {
                            // Local Files selected
                            val navController = findNavController(R.id.nav_host_fragment_content_main)
                            navController.navigate(R.id.nav_reflow)
                        }
                    }
                }
                override fun onTabUnselected(tab: TabLayout.Tab?) {}
                override fun onTabReselected(tab: TabLayout.Tab?) {}
            })
        }
    }

    private fun setupFloatingActionButtons() {
        // Setup floating action button click listeners
        val floatingActionBar: View? = findViewById(R.id.floating_action_bar)
        floatingActionBar?.let { fab ->
            val fabMain: FloatingActionButton? = fab.findViewById(R.id.fab_main)
            fabMain?.setOnClickListener { view ->
                Snackbar.make(view, "Add new item", Snackbar.LENGTH_LONG)
                    .setAction("Action", null).show()
            }
        }
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        val result = super.onCreateOptionsMenu(menu)
        // Using findViewById because NavigationView exists in different layout files
        // between w600dp and w1240dp
        val navView: NavigationView? = findViewById(R.id.nav_view)
        if (navView == null) {
            // The navigation drawer already has the items including the items in the overflow menu
            // We only inflate the overflow menu if the navigation drawer isn't visible
            menuInflater.inflate(R.menu.overflow, menu)
        }
        return result
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when (item.itemId) {
            R.id.nav_settings -> {
                val navController = findNavController(R.id.nav_host_fragment_content_main)
                navController.navigate(R.id.nav_settings)
            }
        }
        return super.onOptionsItemSelected(item)
    }

    override fun onSupportNavigateUp(): Boolean {
        val navController = findNavController(R.id.nav_host_fragment_content_main)
        return navController.navigateUp(appBarConfiguration) || super.onSupportNavigateUp()
    }
}