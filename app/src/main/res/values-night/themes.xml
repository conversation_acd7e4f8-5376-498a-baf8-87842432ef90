<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Dark theme with modern color scheme -->
    <style name="Theme.Testapp" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <!-- Primary brand color -->
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryVariant">@color/primary_variant</item>
        <item name="colorOnPrimary">@color/white</item>

        <!-- Secondary brand color -->
        <item name="colorSecondary">@color/primary_light</item>
        <item name="colorSecondaryVariant">@color/primary_variant</item>
        <item name="colorOnSecondary">@color/white</item>

        <!-- Dark background colors -->
        <item name="android:colorBackground">@color/background_dark</item>
        <item name="colorSurface">@color/card_dark</item>
        <item name="colorOnSurface">@color/foreground_dark</item>
        <item name="colorOnBackground">@color/foreground_dark</item>

        <!-- Status bar color -->
        <item name="android:statusBarColor">@color/background_dark</item>
        <item name="android:windowLightStatusBar">false</item>

        <!-- Modern card styling for dark theme -->
        <item name="cardViewStyle">@style/ModernCardView.Dark</item>
    </style>

    <!-- Dark card style -->
    <style name="ModernCardView.Dark" parent="Widget.MaterialComponents.CardView">
        <item name="cardCornerRadius">12dp</item>
        <item name="cardElevation">4dp</item>
        <item name="cardBackgroundColor">@color/card_dark</item>
        <item name="android:layout_margin">8dp</item>
    </style>
</resources>