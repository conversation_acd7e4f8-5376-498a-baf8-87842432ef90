<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme with modern color scheme -->
    <style name="Theme.Testapp" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <!-- Primary brand color -->
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryVariant">@color/primary_variant</item>
        <item name="colorOnPrimary">@color/white</item>

        <!-- Secondary brand color -->
        <item name="colorSecondary">@color/primary_light</item>
        <item name="colorSecondaryVariant">@color/primary_variant</item>
        <item name="colorOnSecondary">@color/white</item>

        <!-- Background colors -->
        <item name="android:colorBackground">@color/background_light</item>
        <item name="colorSurface">@color/card_light</item>
        <item name="colorOnSurface">@color/foreground_light</item>
        <item name="colorOnBackground">@color/foreground_light</item>

        <!-- Status bar color -->
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        <item name="android:windowLightStatusBar">false</item>

        <!-- Modern card styling -->
        <item name="cardViewStyle">@style/ModernCardView</item>
    </style>

    <style name="Theme.Testapp.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>

    <style name="Theme.Testapp.AppBarOverlay" parent="ThemeOverlay.MaterialComponents.Dark.ActionBar">
        <item name="colorPrimary">@color/primary</item>
        <item name="colorOnPrimary">@color/white</item>
    </style>

    <style name="Theme.Testapp.PopupOverlay" parent="ThemeOverlay.MaterialComponents.Light" />

    <!-- Modern card style -->
    <style name="ModernCardView" parent="Widget.MaterialComponents.CardView">
        <item name="cardCornerRadius">12dp</item>
        <item name="cardElevation">2dp</item>
        <item name="cardBackgroundColor">@color/card_light</item>
        <item name="android:layout_margin">8dp</item>
    </style>

    <!-- Modern button style -->
    <style name="ModernButton" parent="Widget.MaterialComponents.Button">
        <item name="backgroundTint">@color/primary</item>
        <item name="android:textColor">@color/white</item>
        <item name="cornerRadius">8dp</item>
    </style>
</resources>