<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?attr/colorSurface"
    android:elevation="4dp"
    android:orientation="vertical">

    <!-- Header with back button and title -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:paddingHorizontal="16dp">

        <ImageButton
            android:id="@+id/back_button"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:contentDescription="Back"
            android:src="@drawable/ic_arrow_back_24"
            android:tint="?attr/colorOnSurface"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/header_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="sans-serif-medium"
            android:text="Files"
            android:textColor="?attr/colorOnSurface"
            android:textSize="18sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- Tab navigation -->
    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tab_layout"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:background="?attr/colorSurface"
        app:tabGravity="start"
        app:tabIndicatorColor="@color/primary"
        app:tabMode="scrollable"
        app:tabSelectedTextColor="@color/primary"
        app:tabTextColor="@color/subtle_light" />

</LinearLayout>
