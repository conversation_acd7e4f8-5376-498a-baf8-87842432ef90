<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_gravity="bottom|center_horizontal"
    android:layout_margin="16dp"
    android:background="@drawable/floating_bar_background"
    android:elevation="8dp"
    android:orientation="horizontal"
    android:padding="8dp">

    <!-- Navigation Up Button -->
    <com.google.android.material.button.MaterialButton
        android:id="@+id/nav_up_button"
        style="@style/Widget.MaterialComponents.Button.TextButton.Icon"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_margin="4dp"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:contentDescription="Navigate Up"
        android:insetLeft="0dp"
        android:insetTop="0dp"
        android:insetRight="0dp"
        android:insetBottom="0dp"
        app:icon="@drawable/ic_keyboard_arrow_up_24"
        app:iconGravity="textStart"
        app:iconPadding="0dp"
        app:iconSize="24dp"
        app:iconTint="?attr/colorOnSurface" />

    <!-- Navigation Left Button -->
    <com.google.android.material.button.MaterialButton
        android:id="@+id/nav_left_button"
        style="@style/Widget.MaterialComponents.Button.TextButton.Icon"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_margin="4dp"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:contentDescription="Navigate Left"
        android:insetLeft="0dp"
        android:insetTop="0dp"
        android:insetRight="0dp"
        android:insetBottom="0dp"
        app:icon="@drawable/ic_keyboard_arrow_left_24"
        app:iconGravity="textStart"
        app:iconPadding="0dp"
        app:iconSize="24dp"
        app:iconTint="?attr/colorOnSurface" />

    <!-- Navigation Right Button -->
    <com.google.android.material.button.MaterialButton
        android:id="@+id/nav_right_button"
        style="@style/Widget.MaterialComponents.Button.TextButton.Icon"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_margin="4dp"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:contentDescription="Navigate Right"
        android:insetLeft="0dp"
        android:insetTop="0dp"
        android:insetRight="0dp"
        android:insetBottom="0dp"
        app:icon="@drawable/ic_keyboard_arrow_right_24"
        app:iconGravity="textStart"
        app:iconPadding="0dp"
        app:iconSize="24dp"
        app:iconTint="?attr/colorOnSurface" />

    <!-- Main Action Button -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fab_main"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="4dp"
        android:contentDescription="Add"
        android:src="@drawable/ic_add_24"
        app:backgroundTint="@color/primary"
        app:fabSize="normal"
        app:tint="@color/white" />

</LinearLayout>
