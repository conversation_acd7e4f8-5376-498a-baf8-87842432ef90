<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    android:clickable="true"
    android:focusable="true"
    android:foreground="?android:attr/selectableItemBackground"
    app:cardCornerRadius="12dp"
    app:cardElevation="2dp"
    app:cardBackgroundColor="?attr/colorSurface"
    app:strokeWidth="0dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="16dp">

        <!-- Icon container with modern styling -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/icon_container"
            android:layout_width="48dp"
            android:layout_height="48dp"
            android:background="@drawable/icon_background"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/image_view_item_transform"
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:contentDescription="@string/image_view_item_transform_content_description"
                android:src="@drawable/ic_folder_24"
                android:tint="@color/primary"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:src="@drawable/ic_folder_24" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <!-- Content container -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/icon_container"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/text_view_item_transform"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="sans-serif-medium"
                android:textColor="?attr/colorOnSurface"
                android:textSize="16sp"
                tools:text="Classroom" />

            <TextView
                android:id="@+id/text_view_item_subtitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="2dp"
                android:fontFamily="sans-serif"
                android:text="Google Drive"
                android:textColor="@color/subtle_light"
                android:textSize="14sp" />

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</com.google.android.material.card.MaterialCardView>